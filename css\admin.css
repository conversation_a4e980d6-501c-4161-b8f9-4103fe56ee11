/* Modern Admin Dashboard Styles */

/* Global Box Sizing and Overflow Prevention */
*, *::before, *::after {
    box-sizing: border-box;
}

html {
    overflow-x: hidden;
}

body {
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

/* Enhanced CSS Variables for Modern Design */
:root {
    /* Modern Admin Layout Variables */
    --admin-sidebar-width: 280px;
    --admin-sidebar-collapsed: 80px;
    --admin-header-height: 72px;
    --admin-content-padding: 2rem;
    --admin-border-radius: 12px;
    --admin-border-radius-lg: 16px;
    --admin-border-radius-xl: 20px;

    /* Modern Shadow System */
    --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --admin-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Modern Color Palette */
    --admin-primary: #6366f1;
    --admin-primary-hover: #5855eb;
    --admin-primary-light: #e0e7ff;
    --admin-secondary: #64748b;
    --admin-success: #10b981;
    --admin-warning: #f59e0b;
    --admin-error: #ef4444;
    --admin-info: #3b82f6;

    /* Modern Background Colors */
    --admin-bg-primary: #ffffff;
    --admin-bg-secondary: #f8fafc;
    --admin-bg-tertiary: #f1f5f9;
    --admin-surface: #ffffff;
    --admin-surface-hover: #f8fafc;
    --admin-card-bg: #ffffff;
    --admin-input-bg: #ffffff;

    /* Modern Text Colors */
    --admin-text-primary: #0f172a;
    --admin-text-secondary: #475569;
    --admin-text-tertiary: #64748b;
    --admin-text-inverse: #ffffff;

    /* Modern Border Colors */
    --admin-border-light: #f1f5f9;
    --admin-border: #e2e8f0;
    --admin-border-strong: #cbd5e1;

    /* Modern Transitions */
    --admin-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --admin-transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --admin-transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Modern Spacing Scale */
    --admin-space-xs: 0.25rem;
    --admin-space-sm: 0.5rem;
    --admin-space-md: 1rem;
    --admin-space-lg: 1.5rem;
    --admin-space-xl: 2rem;
    --admin-space-2xl: 3rem;
    --admin-space-3xl: 4rem;
}

/* Dark Theme Overrides */
[data-theme="dark"] {
    --admin-bg-primary: #0f172a;
    --admin-bg-secondary: #1e293b;
    --admin-bg-tertiary: #334155;
    --admin-surface: #1e293b;
    --admin-surface-hover: #334155;
    --admin-card-bg: #1e293b;
    --admin-input-bg: #1e293b;

    --admin-text-primary: #f8fafc;
    --admin-text-secondary: #cbd5e1;
    --admin-text-tertiary: #94a3b8;

    --admin-border-light: #334155;
    --admin-border: #475569;
    --admin-border-strong: #64748b;

    --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --admin-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    --admin-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
}

/* Modern Admin Layout */
.admin-layout {
    display: flex;
    min-height: 100vh;
    background: var(--admin-bg-secondary);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    width: 100%;
    overflow-x: hidden;
    position: relative;
}

/* Modern Sidebar Design */
.admin-sidebar {
    width: var(--admin-sidebar-width);
    background: var(--admin-surface);
    border-right: 1px solid var(--admin-border);
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: all var(--admin-transition-slow);
    overflow-y: auto;
    box-shadow: var(--admin-shadow-lg);
    backdrop-filter: blur(20px);
}

.admin-sidebar.collapsed {
    width: var(--admin-sidebar-collapsed);
}

.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: var(--admin-border);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--admin-border-strong);
}

.admin-main {
    flex: 1;
    margin-left: var(--admin-sidebar-width);
    transition: margin-left var(--admin-transition-slow);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    width: calc(100% - var(--admin-sidebar-width));
    max-width: calc(100% - var(--admin-sidebar-width));
    overflow-x: hidden;
}

.admin-main.expanded {
    margin-left: var(--admin-sidebar-collapsed);
    width: calc(100% - var(--admin-sidebar-collapsed));
    max-width: calc(100% - var(--admin-sidebar-collapsed));
}

/* Modern Admin Header */
.admin-header {
    height: var(--admin-header-height);
    background: var(--admin-surface);
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--admin-space-xl);
    position: sticky;
    top: 0;
    z-index: 999;
    backdrop-filter: blur(20px);
    box-shadow: var(--admin-shadow-sm);
}

.admin-header-left {
    display: flex;
    align-items: center;
    gap: var(--admin-space-lg);
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--admin-text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--admin-space-sm);
    border-radius: var(--admin-border-radius);
    transition: all var(--admin-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.sidebar-toggle:hover {
    background: var(--admin-surface-hover);
    color: var(--admin-primary);
    transform: scale(1.05);
}

.admin-header-right {
    display: flex;
    align-items: center;
    gap: var(--admin-space-md);
    flex-shrink: 0;
}

/* Modern Sidebar Navigation */
.sidebar-header {
    padding: var(--admin-space-xl) var(--admin-space-lg);
    border-bottom: 1px solid var(--admin-border);
    text-align: center;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-hover) 100%);
    margin-bottom: var(--admin-space-md);
}

.sidebar-logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: white;
    text-decoration: none;
    letter-spacing: -0.025em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all var(--admin-transition);
}

.sidebar-logo:hover {
    transform: scale(1.05);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.sidebar-nav {
    padding: var(--admin-space-lg) 0;
}

.nav-item {
    margin: var(--admin-space-xs) 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--admin-space-md) var(--admin-space-lg);
    color: var(--admin-text-secondary);
    text-decoration: none;
    transition: all var(--admin-transition);
    border-radius: var(--admin-border-radius);
    margin: 0 var(--admin-space-md);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: var(--admin-primary);
    transform: scaleY(0);
    transition: transform var(--admin-transition);
    border-radius: 0 2px 2px 0;
}

.nav-link:hover {
    background: var(--admin-surface-hover);
    color: var(--admin-primary);
    transform: translateX(4px);
}

.nav-link:hover::before {
    transform: scaleY(1);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--admin-primary-light) 0%, rgba(99, 102, 241, 0.1) 100%);
    color: var(--admin-primary);
    font-weight: 600;
    box-shadow: var(--admin-shadow-sm);
}

.nav-link.active::before {
    transform: scaleY(1);
}

.nav-icon {
    width: 1.25rem;
    margin-right: var(--admin-space-md);
    text-align: center;
    font-size: 1.1rem;
    transition: all var(--admin-transition);
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    transition: all var(--admin-transition);
    font-size: 0.95rem;
}

.admin-sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.admin-sidebar.collapsed .nav-link {
    justify-content: center;
    margin: var(--admin-space-xs) var(--admin-space-sm);
    padding: var(--admin-space-md);
}

.admin-sidebar.collapsed .nav-icon {
    margin-right: 0;
}

.admin-sidebar.collapsed .nav-link::before {
    display: none;
}

/* Modern Admin Content */
.admin-content {
    padding: var(--admin-content-padding);
    min-height: calc(100vh - var(--admin-header-height));
    background: var(--admin-bg-secondary);
    flex: 1;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
}

.page-header {
    margin-bottom: var(--admin-space-2xl);
    padding-bottom: var(--admin-space-lg);
    border-bottom: 1px solid var(--admin-border-light);
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--admin-text-primary);
    margin-bottom: var(--admin-space-sm);
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.page-subtitle {
    color: var(--admin-text-secondary);
    font-size: 1.125rem;
    line-height: 1.6;
    font-weight: 400;
}

/* Modern Dashboard Cards */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--admin-space-xl);
    margin-bottom: var(--admin-space-3xl);
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.dashboard-card {
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-border-radius-lg);
    padding: var(--admin-space-xl);
    box-shadow: var(--admin-shadow-md);
    transition: all var(--admin-transition);
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-hover) 100%);
    opacity: 0;
    transition: opacity var(--admin-transition);
}

.dashboard-card:hover {
    box-shadow: var(--admin-shadow-xl);
    transform: translateY(-4px);
    border-color: var(--admin-primary-light);
}

.dashboard-card:hover::before {
    opacity: 1;
}

.card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--admin-space-lg);
}

.card-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: var(--admin-border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    position: relative;
    box-shadow: var(--admin-shadow-md);
    transition: all var(--admin-transition);
}

.card-icon::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: inherit;
    opacity: 0.1;
    transform: scale(1.5);
    transition: all var(--admin-transition);
}

.dashboard-card:hover .card-icon {
    transform: scale(1.1);
    box-shadow: var(--admin-shadow-lg);
}

.dashboard-card:hover .card-icon::after {
    transform: scale(2);
    opacity: 0.05;
}

.card-icon.users {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}
.card-icon.orders {
    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
}
.card-icon.revenue {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}
.card-icon.products {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--admin-text-primary);
    margin-bottom: var(--admin-space-xs);
    line-height: 1;
    letter-spacing: -0.025em;
}

.card-content p {
    color: var(--admin-text-secondary);
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: var(--admin-space-md);
}

.card-trend {
    display: flex;
    align-items: center;
    gap: var(--admin-space-xs);
    font-size: 0.875rem;
    font-weight: 600;
    padding: var(--admin-space-xs) var(--admin-space-sm);
    border-radius: var(--admin-border-radius);
    width: fit-content;
}

.trend-up {
    color: var(--admin-success);
    background: rgba(16, 185, 129, 0.1);
}
.trend-down {
    color: var(--admin-error);
    background: rgba(239, 68, 68, 0.1);
}

/* Modern Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--admin-space-xs) var(--admin-space-md);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid transparent;
    transition: all var(--admin-transition);
}

.status-badge:hover {
    transform: scale(1.05);
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--admin-success);
    border-color: rgba(16, 185, 129, 0.2);
}
.status-inactive {
    background: rgba(100, 116, 139, 0.1);
    color: var(--admin-secondary);
    border-color: rgba(100, 116, 139, 0.2);
}
.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--admin-warning);
    border-color: rgba(245, 158, 11, 0.2);
}
.status-suspended {
    background: rgba(239, 68, 68, 0.1);
    color: var(--admin-error);
    border-color: rgba(239, 68, 68, 0.2);
}
.status-completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--admin-success);
    border-color: rgba(16, 185, 129, 0.2);
}
.status-cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--admin-error);
    border-color: rgba(239, 68, 68, 0.2);
}

/* Modern Data Tables */
.data-table-container {
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-border-radius-lg);
    overflow: hidden;
    box-shadow: var(--admin-shadow-md);
    transition: all var(--admin-transition);
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.data-table-container:hover {
    box-shadow: var(--admin-shadow-lg);
}

.table-header {
    padding: var(--admin-space-xl);
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--admin-bg-secondary);
}

.table-title {
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--admin-text-primary);
    letter-spacing: -0.025em;
}

.table-actions {
    display: flex;
    gap: var(--admin-space-md);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

/* Table wrapper for horizontal scroll on small screens */
.table-wrapper {
    overflow-x: auto;
    width: 100%;
    max-width: 100%;
}

.table-wrapper::-webkit-scrollbar {
    height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
    background: var(--admin-bg-secondary);
}

.table-wrapper::-webkit-scrollbar-thumb {
    background: var(--admin-border);
    border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
    background: var(--admin-border-strong);
}

.data-table th,
.data-table td {
    padding: var(--admin-space-lg) var(--admin-space-xl);
    text-align: left;
    border-bottom: 1px solid var(--admin-border-light);
    vertical-align: middle;
}

.data-table th {
    background: var(--admin-bg-secondary);
    font-weight: 600;
    color: var(--admin-text-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td {
    color: var(--admin-text-primary);
    font-size: 0.95rem;
}

.data-table tbody tr {
    transition: all var(--admin-transition);
}

.data-table tbody tr:hover {
    background: var(--admin-surface-hover);
    transform: scale(1.001);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* Modern Action Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--admin-space-sm);
    padding: var(--admin-space-md) var(--admin-space-lg);
    border: 1px solid transparent;
    border-radius: var(--admin-border-radius);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--admin-transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-hover) 100%);
    color: white;
    box-shadow: var(--admin-shadow);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--admin-primary-hover) 0%, var(--admin-primary) 100%);
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
}

.btn-secondary {
    background: var(--admin-surface);
    color: var(--admin-text-primary);
    border-color: var(--admin-border);
    box-shadow: var(--admin-shadow-sm);
}

.btn-secondary:hover {
    background: var(--admin-surface-hover);
    border-color: var(--admin-border-strong);
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow);
}

.btn-danger {
    background: linear-gradient(135deg, var(--admin-error) 0%, #dc2626 100%);
    color: white;
    box-shadow: var(--admin-shadow);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, var(--admin-error) 100%);
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
}

.btn-sm {
    padding: var(--admin-space-sm) var(--admin-space-md);
    font-size: 0.75rem;
    border-radius: calc(var(--admin-border-radius) - 2px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: var(--admin-shadow-sm) !important;
}

/* Modern Form Styles */
.form-group {
    margin-bottom: var(--admin-space-xl);
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: var(--admin-space-sm);
    font-weight: 600;
    color: var(--admin-text-primary);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--admin-space-md) var(--admin-space-lg);
    border: 2px solid var(--admin-border);
    border-radius: var(--admin-border-radius);
    background: var(--admin-surface);
    color: var(--admin-text-primary);
    font-size: 0.95rem;
    transition: all var(--admin-transition);
    font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: var(--admin-border-strong);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--admin-text-tertiary);
    font-style: italic;
}

.form-select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

/* Floating Label Style */
.form-floating {
    position: relative;
}

.form-floating .form-input,
.form-floating .form-textarea {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating .form-label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: var(--admin-space-md) var(--admin-space-lg);
    pointer-events: none;
    border: 2px solid transparent;
    transform-origin: 0 0;
    transition: all var(--admin-transition);
    margin-bottom: 0;
    color: var(--admin-text-tertiary);
}

.form-floating .form-input:focus ~ .form-label,
.form-floating .form-input:not(:placeholder-shown) ~ .form-label,
.form-floating .form-textarea:focus ~ .form-label,
.form-floating .form-textarea:not(:placeholder-shown) ~ .form-label {
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--admin-primary);
    font-weight: 600;
}

/* Modern Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--admin-border);
    border-radius: 50%;
    border-top-color: var(--admin-primary);
    animation: spin 1s ease-in-out infinite;
}

.loading-skeleton {
    background: linear-gradient(90deg, var(--admin-border-light) 25%, var(--admin-border) 50%, var(--admin-border-light) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--admin-border-radius);
}

.skeleton-text {
    height: 1rem;
    margin-bottom: var(--admin-space-sm);
}

.skeleton-title {
    height: 1.5rem;
    width: 60%;
    margin-bottom: var(--admin-space-md);
}

.skeleton-card {
    height: 120px;
    margin-bottom: var(--admin-space-lg);
}

/* Modern Animations */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Modern Responsive Design */
@media (max-width: 1366px) {
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--admin-space-lg);
    }

    .admin-content {
        padding: var(--admin-space-lg);
    }

    .admin-search {
        max-width: 300px;
    }
}

@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: var(--admin-space-md);
    }

    .admin-content {
        padding: var(--admin-space-md);
    }

    .admin-search {
        max-width: 250px;
    }

    .table-header {
        flex-direction: column;
        gap: var(--admin-space-md);
        align-items: stretch;
    }

    .table-actions {
        flex-wrap: wrap;
        gap: var(--admin-space-sm);
    }
}

/* Enhanced Mobile Sidebar and Navigation */
@media (max-width: 768px) {
    /* Mobile Sidebar Improvements */
    .admin-sidebar {
        transform: translateX(-100%);
        box-shadow: none;
        width: 280px;
        max-width: 80vw;
        z-index: 1050; /* Higher z-index for mobile */
        transition: transform var(--admin-transition-slow), box-shadow var(--admin-transition-slow);
    }

    .admin-sidebar.mobile-open {
        transform: translateX(0);
        box-shadow: var(--admin-shadow-2xl);
    }

    /* Mobile Overlay for Sidebar */
    .admin-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        opacity: 0;
        visibility: hidden;
        transition: all var(--admin-transition-slow);
    }

    .admin-sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Main Content Mobile Adjustments */
    .admin-main {
        margin-left: 0;
        width: 100%;
        max-width: 100%;
    }

    .admin-main.expanded {
        margin-left: 0;
        width: 100%;
        max-width: 100%;
    }

    /* Mobile Header Improvements */
    .admin-header {
        padding: 0 var(--admin-space-md);
        position: sticky;
        top: 0;
        z-index: 1030;
        backdrop-filter: blur(20px);
    }

    .admin-header-left {
        gap: var(--admin-space-sm);
    }

    .admin-header-right {
        gap: var(--admin-space-xs);
    }

    /* Sidebar Toggle Button Enhancement */
    .sidebar-toggle {
        padding: var(--admin-space-sm);
        border-radius: var(--admin-border-radius);
        background: transparent;
        border: 1px solid var(--admin-border);
        color: var(--admin-text-primary);
        transition: all var(--admin-transition);
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 44px; /* Touch target size */
        min-height: 44px;
    }

    .sidebar-toggle:hover {
        background: var(--admin-bg-hover);
        border-color: var(--admin-border-strong);
    }

    .sidebar-toggle:focus {
        outline: 2px solid var(--admin-primary);
        outline-offset: 2px;
    }

    /* Content and Layout */
    .admin-content {
        padding: var(--admin-space-md);
    }

    .admin-search {
        max-width: 200px;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--admin-space-md);
    }

    /* Table Responsive Improvements */
    .table-header {
        flex-direction: column;
        gap: var(--admin-space-md);
        align-items: stretch;
        padding: var(--admin-space-md);
    }

    .table-actions {
        flex-direction: column;
        gap: var(--admin-space-sm);
    }

    .table-actions .btn {
        width: 100%;
        justify-content: center;
        min-height: 44px; /* Touch target size */
    }

    .data-table {
        font-size: 0.875rem;
        min-width: 600px;
    }

    .data-table th,
    .data-table td {
        padding: var(--admin-space-sm);
        white-space: nowrap;
    }

    /* Mobile Navigation Improvements */
    .nav-link {
        min-height: 48px; /* Better touch targets */
        display: flex;
        align-items: center;
        padding: var(--admin-space-md) var(--admin-space-lg);
    }

    .nav-icon {
        min-width: 24px;
        min-height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .page-title {
        font-size: 1.875rem;
    }

    .card-content h3 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .admin-header {
        padding: 0 var(--admin-space-sm);
    }

    .admin-header-left {
        gap: var(--admin-space-sm);
    }

    .admin-header-right {
        gap: var(--admin-space-xs);
    }

    .admin-content {
        padding: var(--admin-space-sm);
    }

    .admin-search {
        display: none; /* Hide search on very small screens */
    }

    .dashboard-card {
        padding: var(--admin-space-md);
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--admin-space-sm);
    }

    .card-icon {
        width: 2.5rem;
        height: 2.5rem;
    }

    .card-content h3 {
        font-size: 1.5rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .table-header {
        padding: var(--admin-space-sm);
    }

    .data-table th,
    .data-table td {
        padding: var(--admin-space-xs);
        font-size: 0.75rem;
    }

    .btn {
        padding: var(--admin-space-xs) var(--admin-space-sm);
        font-size: 0.75rem;
    }

    .btn-sm {
        padding: var(--admin-space-xs);
        font-size: 0.7rem;
    }
}

/* Additional width constraints for very wide content */
@media (min-width: 1920px) {
    .admin-content {
        max-width: 1600px;
        margin: 0 auto;
    }
}

/* Ensure forms don't overflow */
.form-input,
.form-select,
.form-textarea {
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
}

/* Prevent button text from wrapping */
.btn {
    white-space: nowrap;
}

/* Ensure dashboard cards don't get too wide */
.dashboard-card {
    max-width: 100%;
    box-sizing: border-box;
}

/* Fix potential overflow in card content */
.card-content {
    min-width: 0;
    overflow: hidden;
}

.card-content h3 {
    word-break: break-word;
}

/* Ensure table actions don't overflow */
.table-actions {
    flex-wrap: wrap;
    gap: var(--admin-space-sm);
}

/* Fix breadcrumb overflow */
.breadcrumb {
    flex-wrap: wrap;
    overflow: hidden;
}

.breadcrumb-item {
    min-width: 0;
}

.breadcrumb-link {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Modern Tooltips */
[title] {
    position: relative;
    cursor: pointer;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--admin-text-primary);
    color: var(--admin-surface);
    padding: var(--admin-space-sm) var(--admin-space-md);
    border-radius: var(--admin-border-radius);
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    margin-bottom: var(--admin-space-xs);
    animation: tooltipFadeIn 0.2s ease;
    box-shadow: var(--admin-shadow-lg);
    border: 1px solid var(--admin-border);
}

[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--admin-text-primary);
    z-index: 1000;
    pointer-events: none;
    margin-bottom: -4px;
    animation: tooltipFadeIn 0.2s ease;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(4px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Modern Notification/Toast System */
.toast-container {
    position: fixed;
    top: var(--admin-space-xl);
    right: var(--admin-space-xl);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: var(--admin-space-md);
}

.toast {
    background: var(--admin-surface);
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-border-radius-lg);
    padding: var(--admin-space-lg);
    box-shadow: var(--admin-shadow-xl);
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

.toast::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--admin-primary);
}

.toast.success::before {
    background: var(--admin-success);
}

.toast.warning::before {
    background: var(--admin-warning);
}

.toast.error::before {
    background: var(--admin-error);
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--admin-space-sm);
}

.toast-title {
    font-weight: 600;
    color: var(--admin-text-primary);
    font-size: 0.95rem;
}

.toast-close {
    background: none;
    border: none;
    color: var(--admin-text-tertiary);
    cursor: pointer;
    padding: var(--admin-space-xs);
    border-radius: var(--admin-border-radius);
    transition: all var(--admin-transition);
}

.toast-close:hover {
    background: var(--admin-surface-hover);
    color: var(--admin-text-primary);
}

.toast-message {
    color: var(--admin-text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Modern Modal Styles */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 9998;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--admin-space-lg);
    animation: fadeIn 0.2s ease-out;
}

.modal {
    background: var(--admin-surface);
    border-radius: var(--admin-border-radius-xl);
    box-shadow: var(--admin-shadow-2xl);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
    padding: var(--admin-space-xl);
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--admin-text-primary);
}

.modal-body {
    padding: var(--admin-space-xl);
}

.modal-footer {
    padding: var(--admin-space-xl);
    border-top: 1px solid var(--admin-border);
    display: flex;
    gap: var(--admin-space-md);
    justify-content: flex-end;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Modern Breadcrumb Navigation */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--admin-space-sm);
    font-size: 0.875rem;
    color: var(--admin-text-secondary);
    margin-bottom: var(--admin-space-md);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--admin-space-sm);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--admin-text-tertiary);
    font-weight: 300;
}

.breadcrumb-link {
    color: var(--admin-text-secondary);
    text-decoration: none;
    transition: color var(--admin-transition);
    padding: var(--admin-space-xs) var(--admin-space-sm);
    border-radius: var(--admin-border-radius);
    transition: all var(--admin-transition);
}

.breadcrumb-link:hover {
    color: var(--admin-primary);
    background: var(--admin-surface-hover);
}

.breadcrumb-current {
    color: var(--admin-text-primary);
    font-weight: 600;
}

/* Modern Search Component */
.admin-search {
    position: relative;
    max-width: 400px;
    width: 100%;
    flex-shrink: 1;
    min-width: 0;
}

.admin-search-input {
    width: 100%;
    padding: var(--admin-space-sm) var(--admin-space-md);
    padding-left: 2.5rem;
    border: 2px solid var(--admin-border);
    border-radius: var(--admin-border-radius-lg);
    background: var(--admin-surface);
    color: var(--admin-text-primary);
    font-size: 0.875rem;
    transition: all var(--admin-transition);
}

.admin-search-input:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.admin-search-icon {
    position: absolute;
    left: var(--admin-space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--admin-text-tertiary);
    pointer-events: none;
}

/* Modern Profile Avatar */
.profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-hover) 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--admin-transition);
    border: 2px solid var(--admin-surface);
    box-shadow: var(--admin-shadow);
}

.profile-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--admin-shadow-lg);
}

/* Modern Badge Component */
.modern-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--admin-space-xs) var(--admin-space-sm);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid transparent;
    transition: all var(--admin-transition);
}

.modern-badge.primary {
    background: rgba(99, 102, 241, 0.1);
    color: var(--admin-primary);
    border-color: rgba(99, 102, 241, 0.2);
}

.modern-badge.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--admin-success);
    border-color: rgba(16, 185, 129, 0.2);
}

.modern-badge.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--admin-warning);
    border-color: rgba(245, 158, 11, 0.2);
}

.modern-badge.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--admin-error);
    border-color: rgba(239, 68, 68, 0.2);
}

/* Modern Empty State */
.empty-state {
    text-align: center;
    padding: var(--admin-space-3xl);
    color: var(--admin-text-secondary);
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--admin-text-tertiary);
    margin-bottom: var(--admin-space-lg);
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--admin-text-primary);
    margin-bottom: var(--admin-space-sm);
}

.empty-state-description {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: var(--admin-space-xl);
}
